import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';

class WalletBinding extends Bindings {
  @override
  void dependencies() {
    // Register WalletController as a singleton
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Register all core controllers that should be available app-wide
    Get.put<WalletController>(WalletController(), permanent: true);
  }
}
