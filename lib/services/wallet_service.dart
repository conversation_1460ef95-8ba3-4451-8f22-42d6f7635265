// ignore_for_file: avoid_print

import 'dart:async';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../controllers/wallet_controller.dart';
import '../models/transaction_model.dart';

// Legacy Transaction class for backward compatibility
class Transaction {
  final String id;
  final String type;
  final double amount;
  final String description;
  final DateTime timestamp;
  final String status;
  final String? postId;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    required this.status,
    this.postId,
  });

  factory Transaction.fromTransactionModel(TransactionModel model) {
    return Transaction(
      id: model.id,
      type: model.type.name,
      amount: model.amount,
      description: model.description,
      timestamp: model.timestamp,
      status: model.status.name,
      postId: model.postId,
    );
  }
}

/// Legacy WalletService that now delegates to GetX WalletController
/// This maintains backward compatibility while using GetX state management
class WalletService {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal();

  WalletController get _walletController => Get.find<WalletController>();

  double get currentBalance => _walletController.balance;
  bool get isInitialized => _walletController.isInitialized;

  /// Initialize the wallet service
  Future<void> initialize() async {
    await _walletController.initialize();
  }

  Future<void> dispose() async {
    // GetX WalletController handles its own disposal
  }

  // ---------- Public API  ----------

  Future<bool> addFunds(double amount) async {
    return await _walletController.addFunds(amount);
  }

  Future<bool> deductBalance(
    double amount,
    String description, {
    String? postId,
  }) async {
    return await _walletController.deductFunds(
      amount: amount,
      description: description,
      postId: postId,
    );
  }

  Future<bool> withdrawFunds(double amount, String bankAccount) async {
    // TODO: Implement withdrawal through WalletController
    // For now, return false as this feature needs to be implemented in the new architecture
    return false;
  }

  Future<List<Transaction>> getTransactionHistory({int limit = 50}) async {
    final transactions = _walletController.transactions.take(limit).toList();
    return transactions
        .map((t) => Transaction.fromTransactionModel(t))
        .toList();
  }

  Future<double> getTotalEarnings() async {
    return _walletController.totalEarnings;
  }

  Future<double> getTotalSpent() async {
    return _walletController.totalSpent;
  }

  String formatCurrency(double amount) =>
      NumberFormat.currency(symbol: '\$', decimalDigits: 2).format(amount);

  // Dev utility
  Future<bool> simulatePayment(double amount) => addFunds(amount);
}
