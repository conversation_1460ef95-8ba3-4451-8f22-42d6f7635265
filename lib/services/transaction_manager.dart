import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/transaction_model.dart';

class TransactionManager {
  static final TransactionManager _instance = TransactionManager._internal();
  factory TransactionManager() => _instance;
  TransactionManager._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  DocumentReference<Map<String, dynamic>>? _walletDoc;
  CollectionReference<Map<String, dynamic>>? _transactionsCol;
  
  bool _isInitialized = false;
  String? _userId;

  bool get isInitialized => _isInitialized;

  Future<void> initialize(String userId) async {
    if (_isInitialized && _userId == userId) return;

    _userId = userId;
    _walletDoc = _firestore.collection('wallets').doc(userId);
    _transactionsCol = _walletDoc!.collection('transactions');
    _isInitialized = true;
  }

  /// Adds a transaction and updates the wallet balance atomically
  Future<String> addTransaction(TransactionModel transaction, double newBalance) async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      // Use a batch write to ensure atomicity
      final batch = _firestore.batch();
      
      // Add the transaction
      final transactionRef = _transactionsCol!.doc();
      batch.set(transactionRef, transaction.toMap());
      
      // Update the wallet balance
      batch.update(_walletDoc!, {
        'balance': newBalance,
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Commit the batch
      await batch.commit();
      
      return transactionRef.id;
    } catch (e) {
      debugPrint('Error adding transaction: $e');
      rethrow;
    }
  }

  /// Adds a transaction with retry logic
  Future<String> addTransactionWithRetry(
    TransactionModel transaction, 
    double newBalance, {
    int maxRetries = 3,
  }) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < maxRetries) {
      try {
        return await addTransaction(transaction, newBalance);
      } catch (e) {
        attempts++;
        lastException = e is Exception ? e : Exception(e.toString());
        
        if (attempts < maxRetries) {
          // Exponential backoff
          await Future.delayed(Duration(milliseconds: 100 * (1 << attempts)));
        }
      }
    }

    throw lastException ?? Exception('Failed to add transaction after $maxRetries attempts');
  }

  /// Updates a transaction status
  Future<void> updateTransactionStatus(String transactionId, TransactionStatus status) async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      await _transactionsCol!.doc(transactionId).update({
        'status': status.name,
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating transaction status: $e');
      rethrow;
    }
  }

  /// Gets transaction history with pagination
  Future<List<TransactionModel>> getTransactionHistory({
    int limit = 50,
    DocumentSnapshot? startAfter,
    TransactionType? type,
    TransactionStatus? status,
  }) async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      Query<Map<String, dynamic>> query = _transactionsCol!
          .orderBy('timestamp', descending: true);

      // Apply filters
      if (type != null) {
        query = query.where('type', isEqualTo: type.name);
      }
      if (status != null) {
        query = query.where('status', isEqualTo: status.name);
      }

      // Apply pagination
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }
      
      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => TransactionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting transaction history: $e');
      rethrow;
    }
  }

  /// Gets a specific transaction by ID
  Future<TransactionModel?> getTransaction(String transactionId) async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      final doc = await _transactionsCol!.doc(transactionId).get();
      if (doc.exists) {
        return TransactionModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting transaction: $e');
      rethrow;
    }
  }

  /// Gets transaction statistics
  Future<Map<String, double>> getTransactionStats() async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      final snapshot = await _transactionsCol!
          .where('status', isEqualTo: TransactionStatus.completed.name)
          .get();

      double totalEarnings = 0.0;
      double totalSpent = 0.0;
      double totalPending = 0.0;

      for (final doc in snapshot.docs) {
        final transaction = TransactionModel.fromFirestore(doc);
        
        if (transaction.isCredit) {
          totalEarnings += transaction.amount;
        } else if (transaction.isDebit) {
          totalSpent += transaction.amount;
        }
      }

      // Get pending transactions
      final pendingSnapshot = await _transactionsCol!
          .where('status', isEqualTo: TransactionStatus.pending.name)
          .get();

      for (final doc in pendingSnapshot.docs) {
        final transaction = TransactionModel.fromFirestore(doc);
        totalPending += transaction.amount;
      }

      return {
        'totalEarnings': totalEarnings,
        'totalSpent': totalSpent,
        'totalPending': totalPending,
        'netBalance': totalEarnings - totalSpent,
      };
    } catch (e) {
      debugPrint('Error getting transaction stats: $e');
      rethrow;
    }
  }

  /// Processes a refund transaction
  Future<String> processRefund({
    required String originalTransactionId,
    required double amount,
    required String reason,
  }) async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      // Get the original transaction
      final originalTransaction = await getTransaction(originalTransactionId);
      if (originalTransaction == null) {
        throw Exception('Original transaction not found');
      }

      if (originalTransaction.amount < amount) {
        throw Exception('Refund amount cannot exceed original transaction amount');
      }

      // Get current balance
      final walletDoc = await _walletDoc!.get();
      final currentBalance = (walletDoc.data()?['balance'] ?? 0.0).toDouble();

      // Create refund transaction
      final refundTransaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.refund,
        amount: amount,
        description: 'Refund: $reason',
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        metadata: {
          'originalTransactionId': originalTransactionId,
          'refundReason': reason,
        },
      );

      // Add refund transaction and update balance
      return await addTransaction(refundTransaction, currentBalance + amount);
    } catch (e) {
      debugPrint('Error processing refund: $e');
      rethrow;
    }
  }

  /// Validates transaction consistency
  Future<bool> validateTransactionConsistency() async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      // Get all completed transactions
      final transactions = await getTransactionHistory(limit: 1000);
      final completedTransactions = transactions
          .where((t) => t.status == TransactionStatus.completed)
          .toList();

      // Calculate expected balance
      double expectedBalance = 0.0;
      for (final transaction in completedTransactions) {
        if (transaction.isCredit) {
          expectedBalance += transaction.amount;
        } else if (transaction.isDebit) {
          expectedBalance -= transaction.amount;
        }
      }

      // Get actual balance
      final walletDoc = await _walletDoc!.get();
      final actualBalance = (walletDoc.data()?['balance'] ?? 0.0).toDouble();

      // Check if balances match (with small tolerance for floating point precision)
      const tolerance = 0.01;
      final isConsistent = (expectedBalance - actualBalance).abs() < tolerance;

      if (!isConsistent) {
        debugPrint('Balance inconsistency detected: expected $expectedBalance, actual $actualBalance');
      }

      return isConsistent;
    } catch (e) {
      debugPrint('Error validating transaction consistency: $e');
      return false;
    }
  }

  /// Repairs balance inconsistency
  Future<void> repairBalanceInconsistency() async {
    if (!_isInitialized) {
      throw StateError('TransactionManager not initialized');
    }

    try {
      // Get all completed transactions
      final transactions = await getTransactionHistory(limit: 1000);
      final completedTransactions = transactions
          .where((t) => t.status == TransactionStatus.completed)
          .toList();

      // Calculate correct balance
      double correctBalance = 0.0;
      for (final transaction in completedTransactions) {
        if (transaction.isCredit) {
          correctBalance += transaction.amount;
        } else if (transaction.isDebit) {
          correctBalance -= transaction.amount;
        }
      }

      // Update balance
      await _walletDoc!.update({
        'balance': correctBalance,
        'lastUpdated': FieldValue.serverTimestamp(),
        'repairedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Balance repaired to: $correctBalance');
    } catch (e) {
      debugPrint('Error repairing balance: $e');
      rethrow;
    }
  }
}
